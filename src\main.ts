import { app, BrowserWindow } from "electron";
import registerListeners from "./helpers/ipc/listeners-register";
// "electron-squirrel-startup" seems broken when packaging with vite
//import started from "electron-squirrel-startup";
import path from "path";
import { spawn, ChildProcess } from "child_process";
import {
  installExtension,
  REACT_DEVELOPER_TOOLS,
} from "electron-devtools-installer";

// Importiere den Datenbankservice
import databaseService from "./services/database";

// Importiere die portable Pfad-Konfiguration
import { setupPortablePaths } from "./portable-paths";

// Backend-Server Prozess
let backendProcess: ChildProcess | null = null;

const inDevelopment = process.env.NODE_ENV === "development";

// Konfiguriere portable Pfade, wenn die App nicht im Entwicklungsmodus läuft
// oder wenn die Umgebungsvariable PORTABLE_EXECUTABLE_DIR gesetzt ist
if (!inDevelopment || process.env.PORTABLE_EXECUTABLE_DIR) {
  const paths = setupPortablePaths();
  console.log('Portable Pfade konfiguriert:', paths);
}

// Backend Server Management
async function startBackendServer() {
  try {
    console.log('🚀 Starte AI ChatBot Backend Server...');
    
    const backendPath = path.join(__dirname, '../../backend');
    const serverScript = path.join(backendPath, 'src/server.ts');
    
    // Prüfe ob Backend-Verzeichnis existiert
    const fs = require('fs');
    if (!fs.existsSync(backendPath)) {
      console.warn('⚠️ Backend-Verzeichnis nicht gefunden:', backendPath);
      return false;
    }

    // Starte Backend-Server mit ts-node
    backendProcess = spawn('npx', ['ts-node', serverScript], {
      cwd: backendPath,
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });

    backendProcess.stdout?.on('data', (data) => {
      console.log(`🤖 Backend: ${data.toString().trim()}`);
    });

    backendProcess.stderr?.on('data', (data) => {
      console.error(`🤖 Backend Error: ${data.toString().trim()}`);
    });

    backendProcess.on('close', (code) => {
      console.log(`🤖 Backend Server beendet mit Code: ${code}`);
      backendProcess = null;
    });

    // Warte kurz auf Server-Start
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Teste Server-Verfügbarkeit mit native Node.js http module
    try {
      const http = require('http');
      const testPromise = new Promise<boolean>((resolve) => {
        const req = http.get('http://localhost:3001/api/health', (res: any) => {
          if (res.statusCode === 200) {
            console.log('✅ AI ChatBot Backend Server erfolgreich gestartet!');
            resolve(true);
          } else {
            console.warn('⚠️ Backend Health-Check fehlgeschlagen, Status:', res.statusCode);
            resolve(false);
          }
        });
        
        req.on('error', () => {
          console.warn('⚠️ Backend nicht erreichbar');
          resolve(false);
        });
        
        req.setTimeout(5000, () => {
          req.destroy();
          resolve(false);
        });
      });
      
      return await testPromise;
    } catch (error) {
      console.warn('⚠️ Backend Health-Check fehlgeschlagen:', error);
    }
    
    return false;
  } catch (error) {
    console.error('❌ Fehler beim Starten des Backend Servers:', error);
    return false;
  }
}

function stopBackendServer() {
  if (backendProcess) {
    console.log('🛑 Stoppe AI ChatBot Backend Server...');
    backendProcess.kill();
    backendProcess = null;
  }
}

async function createWindow() {
  const preload = path.join(__dirname, "preload.js");
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      devTools: !inDevelopment, // DevTools nur in der Entwicklung aktivieren
      contextIsolation: true,
      nodeIntegration: true,
      nodeIntegrationInSubFrames: false,
      webSecurity: false,
      preload: preload,
    },
    // Standardeinstellung für die Titelleiste verwenden, damit sie sichtbar ist
    // titleBarStyle: "hidden" wurde entfernt
    frame: true, // Stellt sicher, dass der Fensterrahmen angezeigt wird
    show: false,
    // Vollbild-Konfiguration hinzufügen
    fullscreen: false, // Starte nicht im echten Vollbild
    backgroundColor: '#ffffff' // Setze weißen Hintergrund für hellen Modus
  });
  
  // DevTools nur in der Entwicklung automatisch öffnen
  if (inDevelopment) {
    mainWindow.webContents.openDevTools();
  }
  
  // Event-Listener für das Laden des Fensters
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    // Maximiere das Fenster nach dem Anzeigen (außer in der Entwicklung)
    if (!inDevelopment) {
      mainWindow.maximize();
    }
  });
  
  registerListeners(mainWindow);

  try {
    if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
      await mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
      console.log(`Development server URL: ${MAIN_WINDOW_VITE_DEV_SERVER_URL}`);
    } else {
      // Korrigiere den Pfad für portable Builds
      // Im portable Build ist __dirname = .vite/build, die index.html liegt in .vite/renderer/main_window/
      const indexPath = path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`);
      console.log(`Loading from file: ${indexPath}`);
      console.log(`__dirname: ${__dirname}`);
      console.log(`MAIN_WINDOW_VITE_NAME: ${MAIN_WINDOW_VITE_NAME}`);
      await mainWindow.loadFile(indexPath);
    }
  } catch (error) {
    console.error('Failed to load app:', error);
  }
}

async function installExtensions() {
  try {
    const result = await installExtension(REACT_DEVELOPER_TOOLS);
    console.log(`Extensions installed successfully: ${result.name}`);
  } catch {
    console.error("Failed to install extensions");
  }
}

// Initialisiere die App und die Datenbank
app.whenReady().then(async () => {
  try {
    // Initialisiere die Datenbank
    try {
      console.log('Datenbank wurde erfolgreich initialisiert.');
      
      // Teste die Datenbankverbindung durch Abrufen der Service-Level-Daten
      const serviceLevelData = databaseService.getServiceLevelData();
      console.log(`Service-Level-Daten verfügbar: ${serviceLevelData.length} Einträge`);
      
    } catch (error) {
      console.error('Fehler bei der Datenbankinitialisierung:', error instanceof Error ? error.message : String(error));
    }
    
    // Starte Backend Server für AI ChatBot
    await startBackendServer();
    
    await createWindow();
    
    if (inDevelopment) {
      await installExtensions();
    }
  } catch (error) {
    console.error('Fehler beim Starten der Anwendung:', error);
  }
});

//osX only
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    stopBackendServer();
    app.quit();
  }
});

app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Cleanup beim Beenden der App
app.on("before-quit", () => {
  stopBackendServer();
});

app.on("will-quit", () => {
  stopBackendServer();
});
//osX only ends
