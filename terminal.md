PS D:\my_ai\4-Lapp\SFM-Electron> npm run start

> lapp-dashboard@1.0.0 start
> electron-forge start

✔ Checking your system
✔ Locating application
✔ Loading configuration
✔ Preparing native dependencies: 2 / 2 [1s]
✔ Running generateAssets hook
✔ Running preStart hook

➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
vite v6.3.5 building for development...

watching for file changes...
vite v6.3.5 building for development...

watching for file changes...

build started...

build started...
transforming (1) src\preload.ts
transforming (1) src\main.ts
transforming (7) src\helpers\ipc\window\window-channels.ts
✓ 7 modules transformed.
transforming (17) src\helpers\ipc\window\window-channels.ts
rendering chunks (1)...
transforming (32) node_modules\process-nextick-args\index.js
computing gzip size (0)...
computing gzip size (1)...
.vite/build/preload.js  6.26 kB │ gzip: 1.41 kB
built in 275ms.
transforming (74) node_modules\pako\lib\zlib\deflate.js
✓ 131 modules transformed.
rendering chunks (1)...
computing gzip size (0)...
computing gzip size (1)...
.vite/build/main.js  388.74 kB │ gzip: 75.94 kB
built in 754ms.


Datenbank wurde erfolgreich initialisiert.
Service-Level-Daten verf├╝gbar: 89 Eintr├ñge
­ƒÜÇ Starte AI ChatBot Backend Server...
­ƒñû Backend: Lade .env von: D:\my_ai\4-Lapp\SFM-Electron\.env
­ƒñû Backend: Geladene Umgebungsvariablen: {
  OPENROUTER_API_KEY: 'gesetzt',
  OPENROUTER_PRESET_MODEL: '@preset/lapp'
}
­ƒñû Backend: OpenAI-Client erfolgreich initialisiert mit OpenRouter
Aktuelles Verzeichnis: D:\my_ai\4-Lapp\SFM-Electron\backend
Prisma-Schema-Pfad: D:\my_ai\4-Lapp\SFM-Electron\backend\prisma\schema.prisma
­ƒñû Backend: Datenbank-Pfad: D:\my_ai\4-Lapp\SFM-Electron\database\sfm_dashboard.db
Datenbankdatei existiert!
­ƒñû Backend: Dateigr├Â├ƒe: ********** Bytes
­ƒñû Backend Error: D:\my_ai\4-Lapp\SFM-Electron\node_modules\.prisma\client\default.js:43
    throw new Error('@prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.');
          ^
­ƒñû Backend Error: Error: @prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.       
    at new PrismaClient (D:\my_ai\4-Lapp\SFM-Electron\node_modules\.prisma\client\default.js:43:11)
    at Object.<anonymous> (D:\my_ai\4-Lapp\SFM-Electron\backend\src\server.ts:124:16)
    at Module._compile (node:internal/modules/cjs/loader:1734:14)
    at Module.m._compile (D:\my_ai\4-Lapp\SFM-Electron\backend\node_modules\ts-node\src\index.ts:1618:23)
    at loadTS (node:internal/modules/cjs/loader:1826:10)
    at Object.require.extensions.<computed> [as .ts] (D:\my_ai\4-Lapp\SFM-Electron\backend\node_modules\ts-node\src\index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1469:32)
    at Function._load (node:internal/modules/cjs/loader:1286:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
­ƒñû Backend Server beendet mit Code: 1
ÔÜá´©Å Backend nicht erreichbar
Alle IPC-Handler wurden registriert.
Development server URL: http://localhost:5173
Extensions installed successfully: React Developer Tools
CuttingService: getAblaengereiData aufgerufen mit: { startDate: undefined, endDate: undefined }
CuttingService: SQL Query: SELECT * FROM Ablaengerei ORDER BY Datum ASC Params: []
CuttingService: 3040 Datens├ñtze aus Ablaengerei-Tabelle abgerufen
CuttingService: Erster Datensatz: {
  id: 21294,
  Datum: '2012-06-11',
  cutLagerK220: 0,
  cutLagerR220: 6,
  lagerCut220: 43,
  cutLagerK240: null,
  cutLagerR240: null,
  lagerCut240: null,
  cutTT: null,
  cutTR: null,
  cutRR: null,
  cutGesamt: 227,
  pickCut: null,
  cutLager200: null,
  cutLagerK200: 26,
  lagerCut200: 101
}
CuttingService: Zweiter Datensatz: {
  id: 21293,
  Datum: '2012-06-12',
  cutLagerK220: 0,
  cutLagerR220: 13,
  lagerCut220: 4,
  cutLagerK240: null,
  cutLagerR240: null,
  lagerCut240: null,
  cutTT: null,
  cutTR: null,
  cutRR: null,
  cutGesamt: 58,
  pickCut: null,
  cutLager200: 6,
  cutLagerK200: 63,
  lagerCut200: 36
}
CuttingService: Verf├╝gbare Felder: [
  'id',           'Datum',
  'cutLagerK220', 'cutLagerR220',
  'lagerCut220',  'cutLagerK240',
  'cutLagerR240', 'lagerCut240',
  'cutTT',        'cutTR',
  'cutRR',        'cutGesamt',
  'pickCut',      'cutLager200',
  'cutLagerK200', 'lagerCut200'
]
CuttingService: 3040 von 3040 Datens├ñtzen haben g├╝ltiges YYYY-MM-DD Format
CuttingService: Beispiel g├╝ltiges Datum: {
  id: 21294,
  Datum: '2012-06-11',
  cutLagerK220: 0,
  cutLagerR220: 6,
  lagerCut220: 43,
  cutLagerK240: null,
  cutLagerR240: null,
  lagerCut240: null,
  cutTT: null,
  cutTR: null,
  cutRR: null,
  cutGesamt: 227,
  pickCut: null,
  cutLager200: null,
  cutLagerK200: 26,
  lagerCut200: 101
}
CuttingService: getAblaengereiData aufgerufen mit: { startDate: undefined, endDate: undefined }
CuttingService: SQL Query: SELECT * FROM Ablaengerei ORDER BY Datum ASC Params: []
CuttingService: 3040 Datens├ñtze aus Ablaengerei-Tabelle abgerufen
CuttingService: Erster Datensatz: {
  id: 21294,
  Datum: '2012-06-11',
  cutLagerK220: 0,
  cutLagerR220: 6,
  lagerCut220: 43,
  cutLagerK240: null,
  cutLagerR240: null,
  lagerCut240: null,
  cutTT: null,
  cutTR: null,
  cutRR: null,
  cutGesamt: 227,
  pickCut: null,
  cutLager200: null,
  cutLagerK200: 26,
  lagerCut200: 101
}
CuttingService: Zweiter Datensatz: {
  id: 21293,
  Datum: '2012-06-12',
  cutLagerK220: 0,
  cutLagerR220: 13,
  lagerCut220: 4,
  cutLagerK240: null,
  cutLagerR240: null,
  lagerCut240: null,
  cutTT: null,
  cutTR: null,
  cutRR: null,
  cutGesamt: 58,
  pickCut: null,
  cutLager200: 6,
  cutLagerK200: 63,
  lagerCut200: 36
}
CuttingService: Verf├╝gbare Felder: [
  'id',           'Datum',
  'cutLagerK220', 'cutLagerR220',
  'lagerCut220',  'cutLagerK240',
  'cutLagerR240', 'lagerCut240',
  'cutTT',        'cutTR',
  'cutRR',        'cutGesamt',
  'pickCut',      'cutLager200',
  'cutLagerK200', 'lagerCut200'
]
CuttingService: 3040 von 3040 Datens├ñtzen haben g├╝ltiges YYYY-MM-DD Format
CuttingService: Beispiel g├╝ltiges Datum: {
  id: 21294,
  Datum: '2012-06-11',
  cutLagerK220: 0,
  cutLagerR220: 6,
  lagerCut220: 43,
  cutLagerK240: null,
  cutLagerR240: null,
  lagerCut240: null,
  cutTT: null,
  cutTR: null,
  cutRR: null,
  cutGesamt: 227,
  pickCut: null,
  cutLager200: null,
  cutLagerK200: 26,
  lagerCut200: 101
}
