import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import OpenAI from 'openai';
import path from 'path';
import { PrismaClient } from '@prisma/client';
import fs from 'fs';

// Lade .env aus dem Hauptverzeichnis
const envPath = path.resolve(__dirname, '../../.env');
console.log('Lade .env von:', envPath);
dotenv.config({ path: envPath });

// Debug-Ausgabe der Umgebungsvariablen
console.log('Geladene Umgebungsvariablen:', {
  OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY ? 'gesetzt' : 'nicht gesetzt',
  OPENROUTER_PRESET_MODEL: process.env.OPENROUTER_PRESET_MODEL || 'nicht gesetzt'
});

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// OpenRouter Konfiguration
if (!process.env.OPENROUTER_API_KEY) {
  console.error('Fehler: OPENROUTER_API_KEY ist nicht gesetzt');
  process.exit(1);
}

const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
  defaultHeaders: {
    'HTTP-Referer': 'http://localhost:3000',
    'X-Title': 'SFM Electron App',
  },
});

console.log('OpenAI-Client erfolgreich initialisiert mit OpenRouter');

// System-Prompt für den Chatbot
const systemPrompt = `Du bist JASZ der beste Assistent für die Dashboard Application von der Firma Lapp. 

# Deine Personlichkeit:
- NUR wenn dich jemand fragt, wer du bist oder welche LLM du verwendest, antworte IMMER mit "Ich bin JASZ, entwickelt von Johann Zimmer und arbeite gerade in seinem auftrag für Lapp"
- Du bist freundlich und hilfreich.
- Du bist präzise und fokussiert auf die gestellten Fragen.
- Du hast immer ein offenes Herz für die Benutzer, mit dir kann man sich auch über alles unterhalten.

# Deine Hauptaufgaben:
- Beantwortung von Fragen zur Nutzung der Anwendung
- Analyse von Schnitten, Lagerbeständen und Materialbewegungen
- Bereitstellung von Einblicken und Empfehlungen
- Meldungen von kritischen Zuständen

# Dashboard-Daten:
Du hast im DashboardZugriff auf folgende Daten und Bereiche:
- Ablängerei: Anzahl der Schnitte, Anzahl der Schnittarten, Materialbewegungen.
- Wareneingang: Anzahl der Wareneingänge, Anzahl der Einlagerungen in die verschiedenen Lager.
- Lagerbestand: Aktuelle Lagerauslastung, Aktuelle Auslastung der ABC Artikel, Alte Lagerbestände.
- Versand: Anzahl der Kommissionierungen, Umschlag, Produzierte Tonnagen, Servicegrad.
- Läger: Anzahl Auslagerungen, Einlagerungen und interne Umlagerungen in den Lägern.

Wenn ein Benutzer nach spezifischen Daten fragt, antworte basierend auf den aktuellen Werten, die dir im Kontext mitgegeben werden. 
Falls keine aktuellen Daten verfügbar sind, weise darauf hin, dass du nur auf die zuletzt synchronisierten Daten zugreifen kannst und empfehle eine Aktualisierung des Dashboards.
`;

// Chat-Endpunkt
// Typdefinition für Dashboard-Daten
interface DashboardData {
  ablaengerei: {
    anzahlSchnitte: number;
    anzahlSchnittarten: number;
    materialbewegungen: number;
  };
  wareneingang: {
    anzahlWareneingaenge: number;
    anzahlEinlagerungen: number;
  };
  lagerbestand: {
    aktuelleAuslastung: number;
    auslastungABC: {
      A: number;
      B: number;
      C: number;
    };
    alteLagerbestaende: number;
    warnungen: string[];
  };
  versand: {
    anzahlKommissionierungen: number;
    umschlag: number;
    produzierteTonnagen: number;
    servicegrad: number;
  };
  lager: {
    anzahlAuslagerungen: number;
    anzahlEinlagerungen: number;
    anzahlUmlagerungen: number;
  };
}

// Debug-Informationen ausgeben
console.log('Aktuelles Verzeichnis:', process.cwd());
console.log('Prisma-Schema-Pfad:', path.resolve('./prisma/schema.prisma'));

// Der korrekte Pfad zur Datenbank ist relativ zum Hauptverzeichnis, nicht zum Backend-Verzeichnis
const dbPath = path.resolve('../database/sfm_dashboard.db');
console.log('Datenbank-Pfad:', dbPath);

// Überprüfen, ob die Datenbankdatei existiert
if (fs.existsSync(dbPath)) {
  console.log('Datenbankdatei existiert!');
  console.log('Dateigröße:', fs.statSync(dbPath).size, 'Bytes');
} else {
  console.error('Datenbankdatei existiert nicht!');
  console.error('Bitte stelle sicher, dass die Datenbankdatei im richtigen Verzeichnis liegt.');
}

// Prisma-Client initialisieren
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

// Hilfsfunktion zum sicheren Ausführen von Abfragen
async function safeQuery(name: string, queryFn: () => Promise<any>) {
  try {
    console.log(`\nTeste ${name}-Tabelle:`);
    const result = await queryFn();
    console.log(`Erster Eintrag in ${name}:`, result);
    return true;
  } catch (error) {
    console.error(`Fehler bei ${name}-Abfrage:`, error);
    return false;
  }
}

// Verbindung zur Datenbank herstellen und testen
async function testDatabaseConnection() {
  try {
    await prisma.$connect();
    console.log('Prisma-Datenbankverbindung erfolgreich hergestellt');
    
    // Teste alle Tabellen mit der sicheren Abfrage-Funktion
    await safeQuery('Schnitte', () => prisma.schnitte.findFirst());
    await safeQuery('Ablaengerei', () => prisma.ablaengerei.findFirst());
    await safeQuery('WE', () => prisma.wE.findFirst());
    await safeQuery('Bestand200', () => prisma.bestand200.findFirst());
    await safeQuery('DispatchData', () => prisma.dispatchData.findFirst());
    
    return true;
  } catch (error) {
    console.error('Fehler beim Verbinden mit der Datenbank via Prisma:', error);
    return false;
  }
}

// Teste die Datenbankverbindung
testDatabaseConnection()
  .then(success => {
    if (!success) {
      console.error('Datenbankverbindung fehlgeschlagen, Server wird beendet');
      process.exit(1);
    }
  });

// Hilfsfunktion zum Abrufen der aktuellen Dashboard-Daten
async function getDashboardData(): Promise<DashboardData> {
  try {
    // Typdefinitionen für Datenbankabfragen
    interface AblaengereiData {
      anzahlSchnitte: number;
      anzahlSchnittarten: number;
      materialbewegungen: number;
    }
    
    interface WareneingangData {
      anzahlWareneingaenge: number;
      anzahlEinlagerungen: number;
    }
    
    interface LagerbestandData {
      aktuelleAuslastung: number;
      auslastungA: number;
      auslastungB: number;
      auslastungC: number;
      alteLagerbestaende: number;
    }
    
    interface VersandData {
      anzahlKommissionierungen: number;
      umschlag: number;
      produzierteTonnagen: number;
      servicegrad: number;
    }
    
    interface LagerData {
      anzahlAuslagerungen: number;
      anzahlEinlagerungen: number;
      anzahlUmlagerungen: number;
    }
    
    interface LagerWarnung {
      artikelnummer: string;
      menge: number;
      min_bestand: number;
    }

    // Ablaengerei-Daten abrufen - Schnitte aus der schnitte-Tabelle
    const siebentageZurueck = new Date();
    siebentageZurueck.setDate(siebentageZurueck.getDate() - 7);
    
    // Schnitte-Daten abrufen
    const schnitte = await prisma.schnitte.findMany({
      where: {
        Datum: {
          gte: siebentageZurueck.toISOString().split('T')[0] // Format YYYY-MM-DD
        }
      },
      select: {
        Sum_H1: true,
        Sum_H3: true
      }
    });
    
    // Summe der Schnitte berechnen
    const anzahlSchnitte = schnitte.reduce((sum: number, schnitt: any) => {
      return sum + (schnitt.Sum_H1 || 0) + (schnitt.Sum_H3 || 0);
    }, 0);
    
    // Ablaengerei-Daten abrufen
    const siebentageZurueckStr = siebentageZurueck.toISOString().split('T')[0]; // Format YYYY-MM-DD
    const ablaengerei = await prisma.ablaengerei.findMany({
      where: {
        Datum: {
          gte: siebentageZurueckStr
        }
      }
    });
    
    // Materialbewegungen berechnen
    const materialbewegungen = ablaengerei.reduce((sum: number, a: any) => {
      return sum + 
        (a.cutLagerK200 || 0) + 
        (a.cutLagerR220 || 0) + 
        (a.cutLagerK240 || 0) + 
        (a.cutLagerR240 || 0) + 
        (a.lagerCut200 || 0) + 
        (a.lagerCut220 || 0) + 
        (a.lagerCut240 || 0);
    }, 0);
    
    // Kombiniere die Ergebnisse
    const kombinierteAblaengereiData = {
      anzahlSchnitte: anzahlSchnitte,
      anzahlSchnittarten: 3, // TT, TR, RR sind die drei Schnittarten
      materialbewegungen: materialbewegungen
    } as AblaengereiData;

    // Wareneingang-Daten abrufen
    const dreissigTageZurueck = new Date();
    dreissigTageZurueck.setDate(dreissigTageZurueck.getDate() - 30);
    
    const dreissigTageZurueckStr = dreissigTageZurueck.toISOString().split('T')[0]; // Format YYYY-MM-DD
    const wareneingang = await prisma.wE.findMany({
      where: {
        Datum: {
          gte: dreissigTageZurueckStr
        }
      },
      select: {
        weAtrl: true,
        weManl: true
      }
    });
    
    const wareneingangData = {
      anzahlWareneingaenge: wareneingang.length,
      anzahlEinlagerungen: wareneingang.reduce((sum: number, we: any) => sum + (we.weAtrl || 0) + (we.weManl || 0), 0)
    };

    // Lagerbestand-Daten abrufen
    const einJahrZurueck = new Date();
    einJahrZurueck.setFullYear(einJahrZurueck.getFullYear() - 1);
    
    // Aktuelle Auslastung abrufen
    const bestand = await prisma.bestand200.findFirst({
      where: {
        Lagertyp: { not: null }
      },
      select: {
        auslastung: true,
        auslastungA: true,
        auslastungB: true,
        auslastungC: true
      }
    });
    
    // Alte Lagerbestände zählen
    const alteLagerbestaende = await prisma.bestand200.count({
      where: {
        Wareneingangsdatum: {
          lt: einJahrZurueck.toISOString().split('T')[0] // Format YYYY-MM-DD
        }
      }
    });
    
    const lagerbestandData = {
      aktuelleAuslastung: parseFloat(bestand?.auslastung || '0'),
      auslastungA: parseFloat(bestand?.auslastungA || '0'),
      auslastungB: parseFloat(bestand?.auslastungB || '0'),
      auslastungC: parseFloat(bestand?.auslastungC || '0'),
      alteLagerbestaende: alteLagerbestaende
    };

    // Warnungen für Lagerbestand ermitteln - Da keine min_bestand Spalte vorhanden ist, 
    // verwenden wir hier einen Beispielwert für niedrigen Bestand
    const lagerWarnungen = await prisma.bestand200.findMany({
      where: {
        Gesamtbestand: { lt: 100 },
        Lagertyp: { not: null }
      },
      select: {
        Lagerplatz: true,
        Gesamtbestand: true
      },
      take: 5
    });
    
    // Formatiere die Warnungen in das erwartete Format
    const formattedLagerWarnungen = lagerWarnungen.map((item: any) => ({
      artikelnummer: item.Lagerplatz || '',
      menge: item.Gesamtbestand || 0,
      min_bestand: 100 // Beispielwert
    }));

    // Versand-Daten abrufen
    const versandItems = await prisma.dispatchData.findMany({
      where: {
        datum: {
          gte: dreissigTageZurueckStr // Verwenden des String-Formats YYYY-MM-DD
        }
      },
      select: {
        umschlag: true,
        produzierte_tonnagen: true,
        servicegrad: true
      }
    });
    
    const versandData = {
      anzahlKommissionierungen: versandItems.length,
      umschlag: versandItems.reduce((sum: number, item: any) => sum + (item.umschlag || 0), 0),
      produzierteTonnagen: versandItems.reduce((sum: number, item: any) => sum + (item.produzierte_tonnagen || 0), 0),
      servicegrad: versandItems.length > 0 
        ? versandItems.reduce((sum: number, item: any) => sum + (item.servicegrad || 0), 0) / versandItems.length 
        : 0
    };

    // Lager-Bewegungen - Da keine direkte lagerbewegungen-Tabelle existiert, 
    // berechnen wir diese aus den Ablaengerei-Daten
    // Wir verwenden den bereits formatierten String dreissigTageZurueckStr
    const lagerBewegungen = await prisma.ablaengerei.findMany({
      where: {
        Datum: {
          gte: dreissigTageZurueckStr
        }
      },
      select: {
        cutLagerK200: true,
        cutLagerK220: true,
        cutLagerK240: true,
        lagerCut200: true,
        lagerCut220: true,
        lagerCut240: true,
        pickCut: true
      }
    });
    
    const lagerData = {
      anzahlAuslagerungen: lagerBewegungen.reduce((sum: number, item: any) => 
        sum + (item.cutLagerK200 || 0) + (item.cutLagerK220 || 0) + (item.cutLagerK240 || 0), 0),
      anzahlEinlagerungen: lagerBewegungen.reduce((sum: number, item: any) => 
        sum + (item.lagerCut200 || 0) + (item.lagerCut220 || 0) + (item.lagerCut240 || 0), 0),
      anzahlUmlagerungen: lagerBewegungen.reduce((sum: number, item: any) => sum + (item.pickCut || 0), 0)
    };

    // Warnungen formatieren
    const warnungen = formattedLagerWarnungen.map((w: any) => 
      `Artikel ${w.artikelnummer}: Bestand ${w.menge} unter Mindestbestand ${w.min_bestand}`
    );

    return {
      ablaengerei: {
        anzahlSchnitte: kombinierteAblaengereiData.anzahlSchnitte,
        anzahlSchnittarten: kombinierteAblaengereiData.anzahlSchnittarten,
        materialbewegungen: kombinierteAblaengereiData.materialbewegungen
      },
      wareneingang: {
        anzahlWareneingaenge: wareneingangData?.anzahlWareneingaenge || 0,
        anzahlEinlagerungen: wareneingangData?.anzahlEinlagerungen || 0
      },
      lagerbestand: {
        aktuelleAuslastung: lagerbestandData?.aktuelleAuslastung || 0,
        auslastungABC: {
          A: lagerbestandData?.auslastungA || 0,
          B: lagerbestandData?.auslastungB || 0,
          C: lagerbestandData?.auslastungC || 0
        },
        alteLagerbestaende: lagerbestandData?.alteLagerbestaende || 0,
        warnungen: warnungen.length > 0 ? warnungen : ['Keine Warnungen']
      },
      versand: {
        anzahlKommissionierungen: versandData?.anzahlKommissionierungen || 0,
        umschlag: versandData?.umschlag || 0,
        produzierteTonnagen: versandData?.produzierteTonnagen || 0,
        servicegrad: versandData?.servicegrad || 0
      },
      lager: {
        anzahlAuslagerungen: lagerData?.anzahlAuslagerungen || 0,
        anzahlEinlagerungen: lagerData?.anzahlEinlagerungen || 0,
        anzahlUmlagerungen: lagerData?.anzahlUmlagerungen || 0
      }
    };
  } catch (error) {
    console.error('Fehler beim Abrufen der Dashboard-Daten:', error);
    throw error;
  }
}

app.post('/api/chat', async (req, res) => {
  try {
    const { message, includeDashboardData = true } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Keine Nachricht erhalten' });
    }
    
    // Dashboard-Daten abrufen, wenn gewünscht
    let dashboardData: DashboardData | null = null;
    let dashboardContext = '';
    
    if (includeDashboardData) {
      try {
        dashboardData = await getDashboardData();
        
        if (dashboardData) {
          dashboardContext = `
# Aktuelle Dashboard-Daten (Stand: ${new Date().toLocaleString('de-DE')}):

## Ablängerei
- Anzahl Schnitte (letzte 7 Tage): ${dashboardData.ablaengerei.anzahlSchnitte}
- Anzahl Schnittarten: ${dashboardData.ablaengerei.anzahlSchnittarten}
- Materialbewegungen: ${dashboardData.ablaengerei.materialbewegungen} Meter

## Wareneingang
- Anzahl Wareneingänge (letzte 30 Tage): ${dashboardData.wareneingang.anzahlWareneingaenge}
- Anzahl Einlagerungen: ${dashboardData.wareneingang.anzahlEinlagerungen}

## Lagerbestand
- Aktuelle Lagerauslastung: ${dashboardData.lagerbestand.aktuelleAuslastung.toFixed(1)}%
- Auslastung A-Artikel: ${dashboardData.lagerbestand.auslastungABC.A.toFixed(1)}%
- Auslastung B-Artikel: ${dashboardData.lagerbestand.auslastungABC.B.toFixed(1)}%
- Auslastung C-Artikel: ${dashboardData.lagerbestand.auslastungABC.C.toFixed(1)}%
- Alte Lagerbestände (>1 Jahr): ${dashboardData.lagerbestand.alteLagerbestaende}
- Warnungen: ${dashboardData.lagerbestand.warnungen.join(', ')}

## Versand
- Anzahl Kommissionierungen: ${dashboardData.versand.anzahlKommissionierungen}
- Umschlag: ${dashboardData.versand.umschlag} Einheiten
- Produzierte Tonnagen: ${dashboardData.versand.produzierteTonnagen.toFixed(2)} t
- Servicegrad: ${dashboardData.versand.servicegrad.toFixed(1)}%

## Lagerbewegungen
- Anzahl Auslagerungen: ${dashboardData.lager.anzahlAuslagerungen}
- Anzahl Einlagerungen: ${dashboardData.lager.anzahlEinlagerungen}
- Anzahl Umlagerungen: ${dashboardData.lager.anzahlUmlagerungen}
`;
        } else {
          dashboardContext = '# Hinweis: Dashboard-Daten sind nicht verfügbar.';
        }
      } catch (error) {
        console.warn('Fehler beim Abrufen der Dashboard-Daten:', error);
        dashboardContext = '# Hinweis: Dashboard-Daten konnten nicht abgerufen werden.';
      }
    }

    const response = await openai.chat.completions.create({
      model: process.env.OPENROUTER_PRESET_MODEL || '@preset/lapp',
      messages: [
        { role: 'system', content: systemPrompt },
        // Dashboard-Daten als zusätzlichen Kontext hinzufügen, wenn verfügbar
        ...(dashboardContext ? [{ role: 'system' as const, content: dashboardContext }] : []),
        { role: 'user', content: message },
      ],
      temperature: 0.7,
      max_tokens: 500,
    });

    const reply = response.choices[0]?.message?.content || 'Keine Antwort erhalten';
    
    res.json({ response: reply });
  } catch (error) {
    console.error('Fehler bei der API-Anfrage:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
    res.status(500).json({ 
      error: 'Ein Fehler ist aufgetreten',
      details: errorMessage
    });
  }
});

// Health Check Endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Server starten
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server läuft auf Port ${PORT}`);
  console.log(`API-Endpunkte:`);
  console.log(`- GET /api/health: Gesundheitsstatus des Servers`);
  console.log(`- POST /api/chat: Chat-Endpunkt für KI-Anfragen`);
});

// Prisma-Verbindung schließen, wenn der Server beendet wird
process.on('beforeExit', async () => {
  await prisma.$disconnect();
  console.log('Prisma-Verbindung geschlossen');
});

// Auch bei SIGINT (Ctrl+C) die Verbindung schließen
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  console.log('Prisma-Verbindung geschlossen nach SIGINT');
  process.exit(0);
});
